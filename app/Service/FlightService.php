<?php

namespace App\Service;

use App\Models\Branch;
use App\Models\CanceledParcel;
use App\Models\Cashflow;
use App\Models\FlightBranch;
use App\Models\FlightUserPaymentLog;
use App\Models\goods;
use App\Models\Invoice;
use App\Models\Promotion;
use App\Models\SMSslider;
use App\Models\User;
use App\Support\SMS;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FlightService
{
    public function applyStatus($flight, $selectedBranches, $sms_status, $oldFlightStatus)
    {
        $sms_slider = SMSslider::query()->first();
        $promotion = Promotion::query()->find($flight->promotion_id);

        $change_goods = goods::query()
            ->where("flight_id", $flight->id)
            ->when($flight->flight_parcel_state == 'DELIVERED_OK', function ($q) use ($selectedBranches){
                return $q->whereIn("branch_id", $selectedBranches);
            })
            ->get();

        if ($flight->flight_parcel_state == "WAITING") { //1
            foreach ($change_goods->groupBy('user_id') as $userId => $change_goods) {
                $trackingCodes = [];
                foreach ($change_goods as $parcel) {
                    if ($parcel->flight_parcel_state != 'WAREHOUSE'){
                        $trackingCodes[] = $parcel->tracking_code;
                    }
                    //amanatis statusi sawyobshi misuli
                    $parcel->update(['flight_parcel_state' => 'WAREHOUSE']);
                }

                $user = User:: where("id", $userId)->first();
                if ($user->user_wants_sms == 1 and $sms_status == 1 and !empty($trackingCodes)) {
                    $smsText = $sms_slider->title_ge
                        . PHP_EOL
//                        . implode(', ', $trackingCodes)
//                        . PHP_EOL
                        . $sms_slider->description_ge;
                    (new SMS($user->phone, $smsText, (bool)$sms_status))->send();
                }
            }
        }

        if ($flight->flight_parcel_state == "SENT") { // 2
            foreach ($change_goods->groupBy('user_id') as $userId => $change_good) {
                $trackingCodes = [];
                foreach ($change_good as $parcel) {
                    if ($parcel->flight_parcel_state != 'SENT'){
                        $trackingCodes[] = $parcel->tracking_code;
                    }
                    $parcel->update(['flight_parcel_state' => 'SENT']);

                }

                $user = User:: where("id", $userId)->first();
                if ($user->user_wants_sms == 1 and $sms_status == 1 and !empty($trackingCodes)) {
                    $smsText = $sms_slider->title_ru
                        . PHP_EOL
//                        . implode(', ', $trackingCodes)
//                        . PHP_EOL
                        . $sms_slider->description_ru;
                    (new SMS($user->phone, $smsText, (bool)$sms_status))->send();
                }
            }
        }

        if ($flight->flight_parcel_state == "DELIVERED") { // 3
            foreach ($change_goods as $change_good) {
                if (in_array($change_good->branch_id, $selectedBranches)) {
                    $change_good->flight_parcel_state = 'SENT';
                    $change_good->save();
                }

            }
        }

        if ($flight->flight_parcel_state == "DELAYED") { // 4
            foreach ($change_goods as $change_good) {
                if (in_array($change_good->branch_id, $selectedBranches)) {
                    $change_good->flight_parcel_state = 'SENT';
                    $change_good->save();
                }
            }
        }

        if ($flight->flight_parcel_state == "DELIVERED_OK") { // 5
            $flightBranch = FlightBranch::query()->where('flight_id', $flight->id)->pluck('branch_id');
            $branchTbilisiId = Branch::query()->where('title_ge', 'like', '%თბილისი%')->first()->id ?? null;
            $branchKutaisiId = Branch::query()->where('title_ge', 'like', '%ქუთაისი%')->first()->id ?? null;
            $branchBatumiId  = Branch::query()->where('title_ge', 'like',  '%ბათუმი%')->first()->id ?? null;
            $branchZugdidiId = Branch::query()->where('title_ge', 'like', '%ზუგდიდი%')->first()->id ?? null;
            $branchRustaviId = Branch::query()->where('title_ge', 'like', '%რუსთავი%')->first()->id ?? null;
            $branchDidiDighomiId = Branch::query()->where('title_ge', 'like', '%გიორგი%')->first()->id ?? null;
            $maskvaBranchId = Branch::query()->where('title_ge', 'like', '%გამზირი%')->first()->id ?? null;

            foreach ($change_goods->groupBy('user_id') as $userId => $parcel) {
                //amowmebs calculacia ak tu ara gaketebuli
                try {
                    $smsData = [];
                    $user = User::where("id", $userId)->first();
                    foreach ($parcel as $change_good) {
                        //$flightBranch = FlightBranch::query()->where('flight_id', $flight->id)->pluck('branch_id');

                        if (!$flightBranch->contains($change_good->branch_id)) {
                            FlightBranch::query()->firstOrCreate([
                                'flight_id' => $flight->id,
                                'branch_id' => $change_good->branch_id,
                                'user_id' => auth()->id(),
                            ]);
                            if ($parcel->is_calculated == 1){
                                continue;
                            }
                            // update values for goods

                            $priceToPay = ($change_good->phisicalw * $flight->KG_PRICE_2) * $flight->usd_sell_rate;

                            if ($promotion)
                            {
                                if ($promotion->weight >= $change_good->phisicalw)
                                {
                                    $priceToPay = 0;
                                }
                            }
                            $changeGoodUpdateData = [
                                'flight_parcel_state' => 'RECIEVED',
                                'price_to_pay' => $priceToPay,
                                'is_calculated' => 1,
                            ];

                            if (!$change_good->must_customize)
                            {
                                $smsData[$change_good->branch_id][] = $change_good->tracking_code;
                            }


                            if ($change_good->must_customize and !$change_good->customs_clearance)
                            {
                                $smsData['undeclaredParcels'][] = $change_good->tracking_code;
                                $changeGoodUpdateData['flight_parcel_state'] = 'SUSPENDED';
                            }
                            elseif ($flight->locked == 1 and $change_good?->is_declared == 0)
                            {
                                unset($changeGoodUpdateData['flight_parcel_state']);
                                CanceledParcel::query()->updateOrCreate([
                                    'good_id' => $change_good->id,
                                    'flight_id' => $flight->id
                                ]);
                            }
                            $change_good->update($changeGoodUpdateData);

                            //dasamateblia invoice->id cashlowshi
                            $invoice = new Invoice;
                            $invoice->good_id = $change_good->id;
                            $invoice->amount = $change_good->price_to_pay;
                            //                $invoice->invoice_number =Str::uuid();
                            $invoice->save();

                            //                create new cashflow records
                            $cashflow = new Cashflow;
                            $cashflow->user_id = $change_good->user_id;
                            $cashflow->amount = -1 * abs($change_good->price_to_pay);
                            $cashflow->is_income = '0';
                            $cashflow->invoice_id = $invoice->id;
                            $cashflow->save();

                            //user is balance i cvlileba
                            $user = $user->disableBalancePermission();

                            $log = FlightUserPaymentLog::query()
                                ->where('flight', $flight->flight_number)
                                ->where('user_id', $user->id)
                                ->first()
                            ;
                            if ($log) {
                                $log->amount_to += abs($change_good->price_to_pay);
                            }else{
                                FlightUserPaymentLog::query()->create([
                                    'flight' => $flight->flight_number,
                                    'user_id' => $user->id,
                                    'amount_from' => $user->balance,
                                    'amount_to' => abs($change_good->price_to_pay),
                                ]);
                            }
                            $user->balance = $user->balance + (-1 * abs($change_good->price_to_pay));
                            $user->save();
                        }
                    }
                    //sms per user
                    if ($user->user_wants_sms == 1 and $sms_status==1 and !empty($smsData)) {
                        foreach ($smsData as $branchId => $trackingCodes)
                        {
                            if ($branchId == $branchTbilisiId) {
                                $endOfSmsText = $sms_slider->delivered_to_tbilisi;
                            }elseif ($branchId == $branchKutaisiId) {
                                $endOfSmsText = $sms_slider->delivered_to_kutaisi;
                            }elseif ($branchId == $branchBatumiId) {
                                $endOfSmsText = $sms_slider->delivered_to_batumi;
                            }elseif ($branchId == $branchZugdidiId) {
                                $endOfSmsText = $sms_slider->delivered_to_zugdidi;
                            }elseif ($branchId == $branchRustaviId) {
                                $endOfSmsText = $sms_slider->delivered_to_rustavi;
                            }elseif ($branchId == $branchDidiDighomiId) {
                                $endOfSmsText = $sms_slider->delivered_to_dididighomi;
                            }elseif($branchId == 'undeclaredParcels'){
                                $endOfSmsText = implode(', ', $trackingCodes).' შეჩერებულია, გთხოვთ განაბაჟოთ.';
                            }elseif ($branchId == $maskvaBranchId) {
                                $endOfSmsText = $sms_slider->delivered_to_gamziri;
                            }else{
                                $endOfSmsText = $sms_slider->description_en;
                            }
                            $smsText = $sms_slider->title_en
//                                . PHP_EOL
//                                . implode(', ', $trackingCodes)
                                . PHP_EOL
                                . $endOfSmsText
                            ;
                            (new SMS($user->phone, $smsText, (bool)$sms_status))->send();
                        }
                    }

                } catch (\Exception $e) {
                    Log::error('Error occurred in flight update: ' . $e->getMessage());
                }
            }

        }

    }

}