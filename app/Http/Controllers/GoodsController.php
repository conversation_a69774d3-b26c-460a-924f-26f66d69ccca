<?php

namespace App\Http\Controllers;


use App\Exports\ParcelExport;
use App\Http\Requests\Declaration;
use App\Models\AdminLogParcelUser;
use App\Models\Branch;
use App\Models\CanceledParcel;
use App\Models\Cashflow;
use App\Models\city;
use App\Models\Flight;
use App\Models\FlightBranch;
use App\Models\goods;
use App\Models\Invoice;
use App\Models\ItemCategory;
use App\Models\Parameters;
use App\Models\ParcelCoordinates;
use App\Models\SMSslider;
use App\Models\Terms;
use App\Models\TookOutScanHistory;
use App\Models\User;
use App\Models\UserBranchChangeLog;
use App\Notifications\email;
use App\Service\GoodService;
use App\Service\StatisticService;
use App\Support\SMS;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use function PHPUnit\Framework\isFalse;
use function PHPUnit\Framework\stringContains;


class GoodsController extends Controller
{
    public function __construct(public GoodService $service)
    {
    }

    public function welcome()
    {
        return view('admin.home');
    }
    public function export(Request $request)
    {
        $goods = $this->service->filter($request, false);
        return Excel::download(new ParcelExport($goods), 'parcel.xlsx');

    }

    public function index3(Request $request)
    {
        $branches = Branch::all();
        $categories = ItemCategory::all();

        return view('admin.goods.index3', compact('branches', 'categories'));
    }
    // admin goods index
    public function index()
    {
        $itemcategory = ItemCategory::all();
        $flights = Flight::query()->where('created_at', '>=', now()->subYear())->get();

        return view('admin.goods.index', compact('flights', 'itemcategory'));
    }

    public function Goods_index()
    {
        $itemcategory = ItemCategory::all();
        $flights = Flight::query()->where('created_at', '>=', now()->subYear())->get();
        return view('admin.goods.index2', compact('flights', 'itemcategory'));
    }

    public function parcelScan($tracking_code)
    {
        $goods = Goods::query()->with('flight', 'Branch')
            ->where('tracking_code', $tracking_code)
            ->first();

        if ($goods)
        {
            $goods->small_comment = 'SCANNED';
            $goods->sent_from_china_date = now();
            $goods->save();
        }

        if (!$goods) {
            return response()->json(['error' => 'Tracking code not found.'], 404);
        }

        $userParcelCount = Goods::query()->where('user_id', $goods->user_id)->count();
        $totalParcels = Goods::query()->where('user_id', $goods->user_id)->where('flight_parcel_state', 'RECIEVED')->count();

        return response()->json([
            'tracking_code' => $goods->tracking_code,
            'rec_name' => $goods->rec_name,
            'room_number' => $goods->room_number,
            'branch_name' => $goods->Branch->title_en,
            'flight_number' => $goods->flight->flight_number,
            'weight' => $goods->phisicalw,
            'user_parcel_count' => $userParcelCount,
            'total_parcels' => $totalParcels,
            'admin_comment' => $goods->admin_comment,
            'customs_clearance' => $goods->customs_clearance,
        ]);
    }

    public function index3Data(Request $request)
    {
        $data = $this->service->filter($request);

        $pagination = view('pagination', ['paginator' => $data])->render();

        return response()->json([
            'data' => $data,
            'pagination' => $pagination
        ]);
    }


    //admin goods paginate

    public function paginate2(Request $request)
    {
        $currentPage = $request->input("pagination")['page'];
        // Make sure that you call the static method currentPageResolver()
        // before querying users
        Paginator::currentPageResolver(function () use ($currentPage) {
            return $currentPage;
        });

        $search = $request->input("query")['generalSearch'] ?? null;
        $status = $request->input("query")['status'] ?? null;
        $search_id = $request->input("query")['status'] ?? null;
        $uri = $request->input("query.id");

//        dd($request->input("query"));

        $parcels = goods::where("flight_id", $uri)
            ->orderBy('id', 'desc')
            ->paginate(10);

        $custom = collect([
            "meta" => [
                "page" => $parcels->currentPage(),
                "pages" => $parcels->lastPage(),
                "perpage" => $parcels->perPage(),
                "total" => $parcels->total(),
                "sort" => "desc",
                "field" => "id"
            ]
        ]);

        $data = $custom->merge($parcels);
        return response()->json($data);

    }

    //paginate for flights table

    public function paginate(Request $request)
    {
        $currentPage = $request->input("pagination")['page'];
        // Make sure that you call the static method currentPageResolver()
        // before querying users
        Paginator::currentPageResolver(function () use ($currentPage) {
            return $currentPage;
        });

        $search = $request->input("query")['custom_search'] ?? null;
        $status = $request->input("query")['status'] ?? null;
        $courier = $request->input("query")['courier'] ?? null;
        $customer = $request->input("query")['customer'] ?? null;
        $hasAdminComment = $request->input("query")['has_admin_comment'] ?? null; // "yes", "no" and null
        $declaration = $request->input("query")['declaration'] ?? null;
        $customisation = $request->input("query")['customisation'] ?? null;
        $payment = $request->input("query")['payment'] ?? null;
        $flight_number = $request->input("query")['flight_number'] ?? null;

        $oneYearAgo = now()->subYear();

        $query = goods::with(['flight', 'category', 'User', 'localDistribution.branchBox.branch', 'localDistribution'])
            ->when($hasAdminComment,function ($query, $value){
                if ($value == 'yes')
                {
                    $query->whereNotNull('admin_comment')->where('admin_comment', '!=', '');
                }else{
                    $query->whereNull('admin_comment')->orWhere('admin_comment', '');
                }
            })
            ->where('created_at', '>=', $oneYearAgo)
            ->limit(5)
            ->whereHas('flight', function ($query) use ($flight_number) {
                if (!empty($flight_number)) {
                    $query->where('flight_number', "like", "%" . $flight_number . "%");
                }
            });


        if (!empty($search)) {
            $query = $query->where("rec_name", "like", "%" . $search . "%")
                ->orWhere("tracking_code", "like", "%" . $search . "%")
                ->orWhere("room_number", "like", "%" . $search . "%");
        }

        if (!empty($status)) {
            $query = $query->where("flight_parcel_state", $status);
        }

        if (!empty($courier)) {
            if ($courier == "yes") {
                $query = $query->where("uses_courier", 1);
            } else {
                $query = $query->where("uses_courier", 0);
            }
        }
        //snx filter by snx snk snt
        if (!empty($customer)) {
            if ($customer == "SNX") {

                $query = $query->where('user_room_code', "like", "" . $customer . "%");

            }
            if ($customer == "SNXT") {
                $query = $query->where('user_room_code', "like", "%" . $customer . "%");
            }
            if ($customer == "SNXK") {
                $query = $query->where('user_room_code', "like", "%" . $customer . "%");
            } else {
//                $query = $query->where("uses_courier", 0);
                $query = $query->where('user_room_code', "like", "%" . "SNX" . "%");
            }
        }

        if (!empty($declaration)) {
            if ($declaration == "yes") {
                return $query->where("is_declared", 1);
            } else {
                return $query->where("is_declared", 0);
            }
        }

        if (!empty($customisation)) {
            if ($customisation == "yes") {
                return $query->where("must_customize", 1);
            } else {
                return $query->where("must_customize", 0);
            }
        }

        if (!empty($payment)) {
            if ($payment == "yes") {
                return $query->where("is_payed", 1);
            } else {
                return $query->where("is_payed", 0);
            }
        }



        $per_page = $request->input("pagination")['perpage'] ?? 10;
        $parcels = $query
            ->orderBy('id', 'desc')
            ->paginate($per_page);

        // set color on tracking code
        foreach ($parcels as $key => $parcel)
        {
            $color = '';
            if ($parcel->must_customize and !$parcel->is_payed)
            {
                $color = '#FFCCCB';
            }
            if ($parcel->flight_parcel_state == 'RECIEVED' and $parcel->is_payed)
            {
                $color = 'lightgreen';
            }
            if ($parcel->flight_parcel_state == 'SUSPENDED')
            {
                $color = '#FFD580'; // light orange
            }

            $parcels[$key]['tracking_code_color'] = $color;
        }

        $custom = collect([
            "meta" => [
                "page" => $parcels->currentPage(),
                "pages" => $parcels->lastPage(),
                "perpage" => $parcels->perPage(),
                "total" => $parcels->total(),
                "sort" => "desc",
                "field" => "flight_number"
            ]
        ]);

        $data = $custom->merge($parcels);
        return response()->json($data);

    }

    //admin goods paginate courier

    public function paginate_courier(Request $request)
    {
        $currentPage = $request->input("pagination")['page'];
        // Make sure that you call the static method currentPageResolver()
        // before querying users
        Paginator::currentPageResolver(function () use ($currentPage) {
            return $currentPage;
        });

        $parcels = goods::with('flight', 'category', 'user')
            ->orderBy('id', 'desc')
            ->where("uses_courier", '=', 1)
            ->where("flight_parcel_state", '!=', 'TOOK_OUT')
            ->paginate(50);
//        dd($parcels);

        $custom = collect([
            "meta" => [
                "page" => $parcels->currentPage(),
                "pages" => $parcels->lastPage(),
                "perpage" => $parcels->perPage(),
                "total" => $parcels->total(),
                "sort" => "desc",
                "field" => "flight_number"
            ]
        ]);

        $data = $custom->merge($parcels);
        return response()->json($data);

    }

    //admin goods store
    public function store(Request $request)
    {
        $request->validate([
            "tracking_code" => "required|unique:goods",
            "phisicalw" => "required",
            "flight_id" => "required",
            "user_id" => "required",
//            "category_id" => "required",
            "branch_id" => "required"
//            "flight_parcel_state" => "required",
        ]);

        $goods = goods::create([
            "tracking_code" => $request->input('tracking_code'),
            "phisicalw" => $request->input('phisicalw'),
            "quantity" => $request->input('quantity'),
            "user_id" => $request->input('user_id'),
            "flight_id" => $request->input('flight_id'),
            "category_id" => $request->input('category_id'),
            "price_to_pay" => $request->input('price_to_pay'),
            "branch_id" => $request->input('branch_id'),
        ]);

        // ლოგირება
        \App\Models\AdminActivityLog::createLog(
            'create',
            "ამანათის შექმნა: {$goods->tracking_code}",
            'goods',
            $goods->id,
            null,
            $request->only(['tracking_code', 'phisicalw', 'quantity', 'user_id', 'flight_id', 'category_id', 'price_to_pay', 'branch_id'])
        );
        $sms_slider = SMSslider::query()->first();
        $user = User::find($goods->user_id);
        $goods->room_number = $user->user_room_code;
        $goods->pid = $user->identification;
        $goods->sender_company = 'taobao.com';
        $goods->rec_name = $user->first_name_ge . " " . $user->last_name_ge;
//        amanatis filiali gaxdes momxmareblis archeuli filiali
        $goods->branch_id = $user->branch_id;
        $goods->first_branch_id = $user->branch_id;
        $goods->save();

        $flight = Flight::find($goods->flight_id);
        $goods->arrival_date = $flight->takeout_date;
        $goods->china_delivery_date = $flight->send_date;

        //amanatis statusis minicheba frenis mixedvit
        if ($flight->flight_parcel_state == "WAITING") {
            $goods->flight_parcel_state = "WAREHOUSE";
            if ($user->user_wants_sms == 1) {
                $smsText = $sms_slider->title_ge
                    . PHP_EOL
                    . $goods->tracking_code
                    . PHP_EOL
                    . $sms_slider->description_ge;
                (new SMS($user->phone, $smsText))->send();
            }
        }
        elseif ($flight->flight_parcel_state == "SENT") {
            if ($user->user_wants_sms == 1) {
                $smsText = $sms_slider->title_ru
                    . PHP_EOL
                    . $goods->tracking_code
                    . PHP_EOL
                    . $sms_slider->description_ru;
                (new SMS($user->phone, $smsText))->send();
            }
            $goods->flight_parcel_state = "SENT";
        }
        elseif ($flight->flight_parcel_state == "DELAYED") {
            $goods->flight_parcel_state = "SENT";
        }
        elseif ($flight->flight_parcel_state == "DELIVERED") {
            $goods->flight_parcel_state = "SENT";
        }
        elseif ($flight->flight_parcel_state == "DELIVERED_OK") {
            $branchTbilisiId = Branch::query()->where('title_ge', 'like', '%თბილისი%')->first()->id ?? null;
            $branchKutaisiId = Branch::query()->where('title_ge', 'like', '%ქუთაისი%')->first()->id ?? null;
            $branchBatumiId = Branch::query()->where('title_ge', 'like', '%ბათუმი%')->first()->id ?? null;
            $branchZugdidiId = Branch::query()->where('title_ge', 'like', '%ზუგდიდი%')->first()->id ?? null;
            $branchRustaviId = Branch::query()->where('title_ge', 'like', '%რუსთავი%')->first()->id ?? null;
            $branchDidiDighomiId = Branch::query()->where('title_ge', 'like', '%გიორგი%')->first()->id ?? null;
            $maskvaBranchId = Branch::query()->where('title_ge', 'like', '%გამზირი%')->first()->id ?? null;

            $goods->flight_parcel_state = "RECIEVED";
            $sms_slider = SMSslider::first();

            if ($goods->is_calculated == 0) {
                // update values for goods
//                $param = Parameters::first();
                $goods->flight_parcel_state = 'RECIEVED';
                $goods->sell_rate = $flight->usd_sell_rate;
                $goods->goldex_kg_price = $flight->KG_PRICE_2;
                $goods->price_to_pay = ($goods->phisicalw * $goods->goldex_kg_price) * $goods->sell_rate;
                $goods->is_calculated = 1;
                $goods->save();


                //dasamateblia invoice->id cashlowshi
                $invoice = Invoice::create([
                    "good_id" => $goods->id,
                    "amount" => $goods->price_to_pay

                ]);
//                $invoice->good_id = $goods->id;
//                $invoice->amount = $goods->price_to_pay;
                //                $invoice->invoice_number =Str::uuid();

                $invoice->save();

                //                create new cashflow reccords
                $cashflow = new Cashflow;
                $cashflow->user_id = $goods->user_id;
                $cashflow->amount = -1 * abs($goods->price_to_pay);
                $cashflow->is_income = '0';
                $cashflow->invoice_id = $invoice->id;
                $cashflow->save();


                //user is balance i cvlileba
                $user = User:: where("id", $goods->user_id)->first();
                $user->balance = $user->balance + (-1 * abs($goods->price_to_pay));
                $user->save();

                if ($user->balance > 0) {
                    $goods->is_payed = 1;
                    $goods->save();
                }
                if ($user->user_wants_sms == 1) {
                    $branchId = $goods->branch_id;
                    if ($branchId == $branchTbilisiId) {
                        $endOfSmsText = $sms_slider->delivered_to_tbilisi;
                    }elseif ($branchId == $branchKutaisiId) {
                        $endOfSmsText = $sms_slider->delivered_to_kutaisi;
                    }elseif ($branchId == $branchBatumiId) {
                        $endOfSmsText = $sms_slider->delivered_to_batumi;
                    }elseif ($branchId == $branchZugdidiId) {
                        $endOfSmsText = $sms_slider->delivered_to_zugdidi;
                    }elseif ($branchId == $branchRustaviId) {
                        $endOfSmsText = $sms_slider->delivered_to_rustavi;
                    }elseif ($branchId == $branchDidiDighomiId) {
                        $endOfSmsText = $sms_slider->delivered_to_dididighomi;
                    }elseif ($branchId == $maskvaBranchId) {
                        $endOfSmsText = $sms_slider->delivered_to_gamziri;
                    } else{
                        $endOfSmsText = $sms_slider->description_en;
                    }
                    $smsText = $sms_slider->title_en
                        . PHP_EOL
                        . $goods->tracking_code
                        . PHP_EOL
                        . $endOfSmsText
                    ;
                    (new SMS($user->phone, $smsText))->send();
                }
            }
        }

        $goods->currency_id = '1';
        $goods->country_id = '168';
        $goods->is_calculated = '0';


        $goods->save();

        return redirect()->route("goods.index")->with("success", "ამანათი წარმატებით დაემატა.");
    }

//    store method for add new package customer

    public function create()
    {
        $branches = Branch::all();
        $cities = city::all();
        $flights = Flight::all();
        $users = User::all();
        $itemcategory = ItemCategory::all();
        return view('admin.goods.create', compact("branches", "cities", "flights", "users", "itemcategory"));

    }

    //admin create goods

    public function store_new_package(Request $request)
    {
        $request->validate([
            "tracking_code" => "required|unique:App\Goods"
        ]);
        $goods = goods::create([
            "tracking_code" => $request->input('tracking_code'),

        ]);


//            "flight_parcel_state" => "required",
        $user = Auth::user();
//        dd($user);
        $goods->room_number = $user->user_room_code;
        $goods->user_id = $user->id;
        $goods->pid = $user->identification;
        $goods->flight_parcel_state = "WAITING";
        $goods->rec_name = $user->first_name_ge . " " . $user->last_name_ge;
//        chahardkodebulia da gamosasworebelia frenis id
        $goods->flight_id = '1';
        $goods->save();

        $flight = Flight::find($goods->flight_id);
        $goods->arrival_date = $flight->takeout_date;
        $goods->china_delivery_date = $flight->sent_date;
        $goods->save();

        return redirect()->route("customer.packages")->with("success", "ამანათი დამატებულია");
    }

    //admin edit page
    public function edit($id)
    {
        $goods = goods::query()->where('id', $id)->orWhere('tracking_code', $id)->first();
//        axali damatebuli
        $invoice = Invoice::where("good_id", $goods->id)->first();

        $branches = Branch::all();
        $cities = city::all();
        $flights = Flight::all();
        $users = User::all();
        $itemcategory = ItemCategory::all();
        return view('admin.goods.edit', compact("branches", "cities", "flights", "users", "itemcategory", 'goods', 'invoice'));
    }

    //admin update function
    public function update(goods $goods, Request $request)
    {
        $chamoyvanilia = false;

        // Validate the request
        $request->validate([
            'admin_photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB max
        ]);

        // ძველი მნიშვნელობების შენახვა ლოგირებისთვის
        $oldValues = $goods->only([
            'tracking_code', 'flight_id', 'phisicalw', 'user_id', 'category_id',
            'cord_id', 'branch_id', 'flight_parcel_state', 'price_to_pay',
            'customs_clearance', 'admin_comment', 'admin_photo', 'sms_new'
        ]);

        $old_flight_id = $goods->flight_id;
        $old_price_to_pay = $goods->price_to_pay;
        $old_flight_parcel_state = $goods->flight_parcel_state;
        $old_customs_clearance = $goods->customs_clearance;
        $old_sms_new = $goods->sms_new;
        $text = SMSslider::query()->first();

        $oldUserId = $goods->user_id;

        // Handle photo upload
        $adminPhotoPath = $goods->admin_photo; // Keep existing photo by default

        // Check if user wants to remove current photo
        if ($request->input('remove_photo') == '1') {
            // Delete old photo file if exists
            if ($goods->admin_photo && file_exists(storage_path('app/public/admin_photos/' . $goods->admin_photo))) {
                unlink(storage_path('app/public/admin_photos/' . $goods->admin_photo));
            }
            $adminPhotoPath = null;
        }

        // Handle new photo upload
        if ($request->hasFile('admin_photo')) {
            // Delete old photo file if exists
            if ($goods->admin_photo && file_exists(storage_path('app/public/admin_photos/' . $goods->admin_photo))) {
                unlink(storage_path('app/public/admin_photos/' . $goods->admin_photo));
            }

            // Upload new photo
            $file = $request->file('admin_photo');
            $filename = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
            $extension = $file->getClientOriginalExtension();
            $fileNameToStore = $filename . '_' . time() . '.' . $extension;

            // Create directory if it doesn't exist
            if (!file_exists(storage_path('app/public/admin_photos'))) {
                mkdir(storage_path('app/public/admin_photos'), 0755, true);
            }

            $file->storeAs('public/admin_photos', $fileNameToStore);
            $adminPhotoPath = $fileNameToStore;
        }

//        sms_new
        $goods->update([
            "tracking_code" => $request->input('tracking_code'),
            "flight_id" => $request->input('flight_id'),
            "phisicalw" => $request->input('phisicalw'),
            "user_id" => $request->input('user_id'),
            "category_id" => $request->input('category_id'),
            "cord_id" => $request->input('cord_id'),
            "branch_id" => $request->input('branch_id'),
            "flight_parcel_state" => $request->input('flight_parcel_state'),
            "price_to_pay" => $request->input('price_to_pay'),
            'customs_clearance' => (boolean) $request->input('customs_clearance'),
            'admin_comment' => $request->input('admin_comment'),
            'admin_photo' => $adminPhotoPath,
            'sms_new' => $request->input('sms_new'),
//shesacvlelia rom sheicvalos amanatebshi momxmareblis dasaxeleba
//            "rec_name" => $users->first_name_ge,
        ]);

        if ($old_sms_new != $goods->sms_new) {
            if ($goods->sms_new == 1)
            {
                new SMS($goods->user->phone, $text->suspended_type_one);
            }
            elseif ($goods->sms_new == 2)
            {
                new SMS($goods->user->phone, $text->suspended_type_two);
            }
        }

//        if ($old_flight_parcel_state != $goods->flight_parcel_state and  $goods->flight_parcel_state == 'TOOK_OUT')
//        {
//            TookOutScanHistory::query()->create([
//                'tracking_code' => $goods->tracking_code,
//                'user_id' => auth()->id()
//            ]);
//        }

        if($oldUserId != $goods->user_id)
        {
            $newUser = User::query()->find($goods->user_id);
            $totalClientBuyAmount = goods::query()
                ->where('user_id', $newUser->id)
                ->where('flight_id', $goods->flight_id)
                ->sum('client_buy_amount')
            ;
            $totalClientBuyAmount += $goods->client_buy_amount;
            if ($totalClientBuyAmount >= 300) {
                $goods->update(['must_customize' => 1]);
            }

            if($goods->flight_parcel_state == "RECIEVED" )
            {
                $oldUser = User::query()->find($oldUserId);

                UserBranchChangeLog::query()->where('tracking_code', $goods->tracking_code)->delete();

                $goods->update([
                    'user_declaration_comment' => null,
                    'must_customize' => 0,
                    'is_declared' => 0,
                    'cover_image' => null,
                    'sender_company' => null
                ]);

                $invoice = Invoice::query()->where("good_id", $goods->id)->first();
                $cashflow = Cashflow::query()->where('invoice_id', $invoice->id)->first();
                $cashflow->update(['user_id' => $goods->user_id]);
                $amountFromOldCashflow = abs($cashflow->amount);

                // use balance update
                $oldUser->increment('balance', $amountFromOldCashflow);
                $newUser->balance = $newUser->balance - $amountFromOldCashflow;
                $newUser->save();
            }
            $goods->update(['branch_id' => $newUser->branch_id, 'user_declaration_comment' => null]);
            AdminLogParcelUser::query()->create([
                'new_customer_id' => $goods->user_id,
                'old_customer_id' => $oldUserId,
                'user_id' => \auth()->id(),
            ]);
        }

        $user = User::find($goods->user_id);
        if ($old_customs_clearance == false and (boolean) $request->input('customs_clearance') == true)
        {
            $text = 'თქვენი დეკლარაცია დასრულებულია';
            if($user->user_wants_sms == 1)
            {
                (new SMS($user->phone, $text))->send();
            }
        }
        elseif($old_customs_clearance == true and (boolean) $request->input('customs_clearance') == false)
        {
//            $text = 'განბაჟების პროცესი დასრულებულია, შეგიძლიათ გაიტანოთ ამანათი: '.$goods->tracking_code;
            $text = 'საბაჟო პროცედურა დასრულებულია, შეგიძლიათ გაიტანოთ ამანათ(ებ)ი მითითებული ფილიალიდან.';

            $sms_slider = SMSslider::query()->first();

            if($user->user_wants_sms == 1)
            {
                (new SMS($user->phone, $sms_slider->custom_clearance_text ?? $text))->send();
            }
        }
        //flight aris ukve axali razec gadaitane amanati
        $flight = Flight::find($goods->flight_id);
        $param = Parameters::first();
        $sms_slider = SMSslider::first();
        $goods->room_number = $user->user_room_code;
        $goods->rec_name = $user->first_name_ge . " " . $user->last_name_ge;

        $goods->currency_id = '1';
        $goods->country_id = '168';
        $goods->arrival_date = $flight->takeout_date;
        $goods->china_delivery_date = $flight->send_date;
        //$goods->client_buy_amount = 0;
        $goods->pid = $user->identification;
        $goods->sell_rate = $flight->usd_sell_rate;
//        gavauqmet globaluri kursis ageba da amieridan itvlis reisze mititebuli kursis mixedvit flight is kg price it
//        $goods->goldex_kg_price = $param->price_val;
        $goods->goldex_kg_price = $flight->KG_PRICE_2;
        $goods->rec_name = $user->first_name_ge . " " . $user->last_name_ge;

//        amanatis gadatana tua sxva reisze
        if ($goods->flight_id != $old_flight_id) {

            $goods->sell_rate = $flight->usd_sell_rate;
            $goods->price_to_pay = ($goods->phisicalw * $goods->goldex_kg_price) * $goods->sell_rate;
            $goods->save();
            //amanatis statusis minicheba frenis mixedvit
            if ($flight->flight_parcel_state == "WAITING") {
                $goods->flight_parcel_state = "WAREHOUSE";
            }
            elseif ($flight->flight_parcel_state == "SENT") {
                $goods->flight_parcel_state = "SENT";
            }
            elseif ($flight->flight_parcel_state == "DELAYED") {
                $goods->flight_parcel_state = "SENT";
            }
            elseif ($flight->flight_parcel_state == "DELIVERED") {
                $goods->flight_parcel_state = "SENT";
            }
            elseif ($flight->flight_parcel_state == "DELIVERED_OK") {

                $chamoyvanilia = FlightBranch::query()
                    ->where('flight_id', $flight->id)
                    ->where('branch_id', $goods->branch_id)
                    ->exists()
                ;

                if($chamoyvanilia)
                {
                    $goods->flight_parcel_state = "RECIEVED";
                    $sms_slider = SMSslider::first();
                    if ($goods->is_calculated == 0) {
                        // update values for goods
                        $param = Parameters::first();
                        $goods->flight_parcel_state = 'RECIEVED';
                        $goods->sell_rate = $flight->usd_sell_rate;
                        $goods->goldex_kg_price = $flight->KG_PRICE_2;
                        $goods->price_to_pay = ($goods->phisicalw * $goods->goldex_kg_price) * $goods->sell_rate;
                        $goods->is_calculated = 1;
                        $goods->save();

                        //dasamateblia invoice->id cashlowshi
                        $invoice = Invoice::create([
                            "good_id" => $goods->id,
                            "amount" => $goods->price_to_pay
                        ]);
    //                $invoice->good_id = $goods->id;
    //                $invoice->amount = $goods->price_to_pay;
                        //                $invoice->invoice_number =Str::uuid();

                        $invoice->save();

                        //                create new cashflow reccords
                        $cashflow = new Cashflow;
                        $cashflow->user_id = $goods->user_id;
                        $cashflow->amount = -1 * abs($goods->price_to_pay);
                        $cashflow->is_income = '0';
                        $cashflow->invoice_id = $invoice->id;
                        $cashflow->save();


                        //user is balance i cvlileba
                        $user = User:: where("id", $goods->user_id)->first();
                        $user->balance = $user->balance + (-1 * abs($goods->price_to_pay));
    //                    user balances ro daematos arasworad chamochrili amanatis girebuleba dzveli wonit
    //                    dd($old_price_to_pay,$goods->price_to_pay);
    //                    $user->balance = $user->balance + (abs($old_price_to_pay));
                        $user->save();

                        if ($user->balance > 0) {
                            $goods->is_payed = 1;
                            $goods->save();
                        }

                        //mail notification
    //                    $user->notify(new arrivedgoods($change_good));
                        //sms
                        if ($user->user_wants_sms == 1) {
                            (new SMS($user->phone, $sms_slider->title_en . PHP_EOL . $goods->tracking_code . PHP_EOL . $sms_slider->description_en))->send();
                        }
                        //sms
    //                    (new SMS($user->phone,'tqveni amanati :'.$change_good->tracking_code.'chamosulia'))->send();
                    }
                    elseif ($goods->is_calculated == 1) {
                        //        new code update invoice cashflow and user ballance
    //        calculate price for goods
                        $goods->price_to_pay = ($goods->phisicalw * $goods->goldex_kg_price) * $goods->sell_rate;
                        $goods->is_calculated = 1;
    //        update invoice how much to pay
                        $old_invoice = Invoice::where('good_id', $goods->id)->first();
                        $old_invoice->amount = $goods->price_to_pay;
                        $old_invoice->save();
    //        update cashflow how much to pay
                        $cashflow = Cashflow::where('invoice_id', $old_invoice->id)->first();
                        $cashflow->user_id = $goods->user_id;
                        $cashflow->amount = -1 * abs($goods->price_to_pay);
                        $cashflow->is_income = '0';
    //        $cashflow->invoice_id = $invoice->id;
                        $cashflow->save();

                        //user is balance i cvlileba
                        $user = User:: where("id", $goods->user_id)->first();
                        $user->balance = $user->balance + (-1 * abs($goods->price_to_pay));
    //      user balances ro daematos arasworad chamochrili amanatis girebuleba dzveli wonit
                        $user->balance = $user->balance + (abs($old_price_to_pay));
                        $user->save();

                        if ($user->balance > 0) {
                            $goods->is_payed = 1;
                            $goods->save();
                        }

                        //mail notification
    //                    $user->notify(new arrivedgoods($change_good));
                        //sms
                        if ($user->user_wants_sms == 1) {
                            (new SMS($user->phone, $sms_slider->title_en . PHP_EOL . $goods->tracking_code . PHP_EOL  .$sms_slider->description_en))->send();
                        }
                    }
                }else{
                    $goods->flight_parcel_state = "SENT";
                }
            }
        }

        if ($goods->flight_parcel_state == "RECIEVED" and $old_flight_parcel_state != "RECIEVED") {
            $branchTbilisiId = Branch::query()->where('title_ge', 'like', '%თბილისი%')->first()->id ?? null;
            $branchKutaisiId = Branch::query()->where('title_ge', 'like', '%ქუთაისი%')->first()->id ?? null;
            $branchBatumiId = Branch::query()->where('title_ge', 'like', '%ბათუმი%')->first()->id ?? null;
            $branchZugdidiId = Branch::query()->where('title_ge', 'like', '%ზუგდიდი%')->first()->id ?? null;
            $branchRustaviId = Branch::query()->where('title_ge', 'like', '%რუსთავი%')->first()->id ?? null;
            $branchDidiDighomiId = Branch::query()->where('title_ge', 'like', '%გიორგი%')->first()->id ?? null;
            $maskvaBranchId = Branch::query()->where('title_ge', 'like', '%გამზირი%')->first()->id ?? null;

            $goods->price_to_pay = ($goods->phisicalw * $goods->goldex_kg_price) * $goods->sell_rate;
            $goods->save();
            if ($goods->is_calculated == 0) {
                // update values for goods
                $param = Parameters::first();
                $goods->flight_parcel_state = 'RECIEVED';
                $goods->sell_rate = $flight->usd_sell_rate;
                $goods->goldex_kg_price = $flight->KG_PRICE_2;
                $goods->price_to_pay = ($goods->phisicalw * $goods->goldex_kg_price) * $goods->sell_rate;
                $goods->is_calculated = 1;
                $goods->save();

                //dasamateblia invoice->id cashlowshi
                $invoice = Invoice::create([
                    "good_id" => $goods->id,
                    "amount" => $goods->price_to_pay
                ]);
//                $invoice->good_id = $goods->id;
//                $invoice->amount = $goods->price_to_pay;
                //                $invoice->invoice_number =Str::uuid();

                $invoice->save();

                //                create new cashflow reccords
                $cashflow = new Cashflow;
                $cashflow->user_id = $goods->user_id;
                $cashflow->amount = -1 * abs($goods->price_to_pay);
                $cashflow->is_income = '0';
                $cashflow->invoice_id = $invoice->id;
                $cashflow->save();


                //user is balance i cvlileba


                $user = User:: where("id", $goods->user_id)->first();
                if(!$chamoyvanilia)
                {
                    $user->balance = $user->balance + (-1 * abs($goods->price_to_pay));
                }

//                    user balances ro daematos arasworad chamochrili amanatis girebuleba dzveli wonit
//                    dd($old_price_to_pay,$goods->price_to_pay);
//                    $user->balance = $user->balance + (abs($old_price_to_pay));
                $user->save();

                if ($user->balance > 0) {
                    $goods->is_payed = 1;
                    $goods->save();
                }

                //mail notification
//                    $user->notify(new arrivedgoods($change_good));
                //sms
                if ($user->user_wants_sms == 1) {
                    (new SMS($user->phone, $sms_slider->title_en . PHP_EOL . $goods->tracking_code  . PHP_EOL .  $sms_slider->description_en))->send();
                }
                //sms
//                    (new SMS($user->phone,'tqveni amanati :'.$change_good->tracking_code.'chamosulia'))->send();
            }
            elseif ($goods->is_calculated == 1) {
                //        new code update invoice cashflow and user ballance
//        calculate price for goods
                $goods->price_to_pay = ($goods->phisicalw * $goods->goldex_kg_price) * $goods->sell_rate;
                $goods->is_calculated = 1;
//        update invoice how much to pay
                $old_invoice = Invoice::where('good_id', $goods->id)->first();
                $old_invoice->amount = $goods->price_to_pay;
                $old_invoice->save();
//        update cashflow how much to pay
                $cashflow = Cashflow::where('invoice_id', $old_invoice->id)->first();
                $cashflow->user_id = $goods->user_id;
                $cashflow->amount = -1 * abs($goods->price_to_pay);
                $cashflow->is_income = '0';
                $cashflow->save();
//         user is balance i cvlileba
                $user = User:: where("id", $goods->user_id)->first();
//      user balances ro daematos arasworad chamochrili amanatis girebuleba dzveli wonit
                if(!$chamoyvanilia)
                {
                    $user->balance = $user->balance + (-1 * abs($goods->price_to_pay));
                }
                $user->balance = $user->balance + (abs($old_price_to_pay));

                $user->save();
//      user balance tu dadebitia mashin amanati  chatvalos gadaxdilad.
                if ($user->balance > 0) {
                    $goods->is_payed = 1;
                    $goods->save();
                }
//                    $user->notify(new arrivedgoods($change_good));
                //sms
                if ($user->user_wants_sms == 1) {
                    (new SMS($user->phone, $sms_slider->title_en . PHP_EOL . $goods->tracking_code  . PHP_EOL . $sms_slider->description_en))->send();
                }
            }
            //mail notification
//            $user->notify(new arrivedgoods($goods));
            if ($user->user_wants_sms == 1) {
                $branchId = $goods->branch_id;
                if ($branchId == $branchTbilisiId) {
                    $endOfSmsText = $sms_slider->delivered_to_tbilisi;
                }elseif ($branchId == $branchKutaisiId) {
                    $endOfSmsText = $sms_slider->delivered_to_kutaisi;
                }elseif ($branchId == $branchBatumiId) {
                    $endOfSmsText = $sms_slider->delivered_to_batumi;
                }elseif ($branchId == $branchZugdidiId) {
                    $endOfSmsText = $sms_slider->delivered_to_zugdidi;
                }elseif ($branchId == $branchRustaviId) {
                    $endOfSmsText = $sms_slider->delivered_to_rustavi;
                }elseif ($branchId == $branchDidiDighomiId) {
                    $endOfSmsText = $sms_slider->delivered_to_dididighomi;
                }elseif ($branchId == $maskvaBranchId) {
                    $endOfSmsText = $sms_slider->delivered_to_gamziri;
                } else{
                    $endOfSmsText = $sms_slider->description_en;
                }

                (new SMS($user->phone, $sms_slider->title_en . PHP_EOL . $goods->tracking_code . PHP_EOL . $endOfSmsText))->send();
            }
        }
        if ($goods->flight_parcel_state == "WAREHOUSE" and $old_flight_parcel_state != "WAREHOUSE") {
            //mail notification
//            $user->notify(new warehousegoods($goods));
            if ($user->user_wants_sms == 1) {
                (new SMS($user->phone, $sms_slider->title_ge . PHP_EOL . $goods->tracking_code  . PHP_EOL .  $sms_slider->description_ge))->send();
            }
//            (new SMS($user->phone,'tqven miiget amanati chinetshi :'.$goods->tracking_code.'gtxovt daadeklarirot 24 saatshi'))->send();
        }
        if ($goods->flight_parcel_state == "SENT" and $old_flight_parcel_state != "SENT") {
            //mail notification
//            $user->notify(new sentgoods($goods));
            if ($user->user_wants_sms == 1) {
                (new SMS($user->phone, $sms_slider->title_ru . PHP_EOL . $goods->tracking_code  . PHP_EOL .  $sms_slider->description_ru))->send();
            }
//            (new SMS($user->phone,'tqven miiget amanati gamogzavnilia :'.$goods->tracking_code.'gtxovt daadeklarirot 24 saatshi'))->send();
//
        }
        if ($goods->flight_parcel_state == "SUSPENDED" and $old_flight_parcel_state != "SUSPENDED") {
            //mail notification
//            $user->notify(new sentgoods($goods));
            if ($user->user_wants_sms == 1) {
                (new SMS($user->phone, $sms_slider->title_es . PHP_EOL . $goods->tracking_code  . PHP_EOL .  $sms_slider->description_es))->send();
            }
//            (new SMS($user->phone,'tqven miiget amanati gamogzavnilia :'.$goods->tracking_code.'gtxovt daadeklarirot 24 saatshi'))->send();
//
        }
        if ($goods->flight_parcel_state == "RECIEVED" and $old_flight_parcel_state != "SUSPENDED"){
            $goods->update(['flight_parcel_state' => 'RECIEVED']);


            $branchTbilisiId = Branch::query()->where('title_ge', 'like', '%თბილისი%')->first()->id ?? null;
            $branchKutaisiId = Branch::query()->where('title_ge', 'like', '%ქუთაისი%')->first()->id ?? null;
            $branchBatumiId  = Branch::query()->where('title_ge', 'like',  '%ბათუმი%')->first()->id ?? null;
            $branchZugdidiId = Branch::query()->where('title_ge', 'like', '%ზუგდიდი%')->first()->id ?? null;
            $branchRustaviId = Branch::query()->where('title_ge', 'like', '%რუსთავი%')->first()->id ?? null;
            $branchDidiDighomiId = Branch::query()->where('title_ge', 'like', '%გიორგი%')->first()->id ?? null;
            $maskvaBranchId = Branch::query()->where('title_ge', 'like', '%გამზირი%')->first()->id ?? null;


            $branchId = $goods->branch_id;
            if ($branchId == $branchTbilisiId) {
                $endOfSmsText = $sms_slider->delivered_to_tbilisi;
            }
            elseif ($branchId == $branchKutaisiId) {
                $endOfSmsText = $sms_slider->delivered_to_kutaisi;
            }
            elseif ($branchId == $branchBatumiId) {
                $endOfSmsText = $sms_slider->delivered_to_batumi;
            }
            elseif ($branchId == $branchZugdidiId) {
                $endOfSmsText = $sms_slider->delivered_to_zugdidi;
            }
            elseif ($branchId == $branchRustaviId) {
                $endOfSmsText = $sms_slider->delivered_to_rustavi;
            }
            elseif ($branchId == $branchDidiDighomiId) {
                $endOfSmsText = $sms_slider->delivered_to_dididighomi;
            }
            elseif ($branchId == $maskvaBranchId) {
                $endOfSmsText = $sms_slider->delivered_to_gamziri;
            }
            else{
                $endOfSmsText = $sms_slider->description_en;
            }

            $smsText = $sms_slider->title_en
                . PHP_EOL
                . $endOfSmsText
            ;

            if ($user->user_wants_sms == 1)
            {
                (new SMS($user->phone, $smsText))->send();
            }
        }


        $goods->save();

        // ლოგირება
        $newValues = $goods->only([
            'tracking_code', 'flight_id', 'phisicalw', 'user_id', 'category_id',
            'cord_id', 'branch_id', 'flight_parcel_state', 'price_to_pay',
            'customs_clearance', 'admin_comment', 'sms_new'
        ]);

        \App\Models\AdminActivityLog::createLog(
            'update',
            "ამანათის განახლება: {$goods->tracking_code}",
            'goods',
            $goods->id,
            $oldValues,
            $newValues
        );

//        $user->notify(new arrivedgoods($goods));

        return redirect()->route("goods.index", $goods->id)->with("success", "ამანათი წარმატებით დარექაქტირდა.");
    }

    public function bulkUpdateStatus(Request $request): JsonResponse
    {
        $success = (new GoodService)->updateStatus(
            $request->good_ids,
            $request->status,
            $request->filters,
            $request->select_all ?? false
        );

        return response()->json(['success' => $success]);
    }

    public function clearanceChange(Request $request): JsonResponse
    {
        $goodIds = $request->input('good_ids');
        $isReceived = $request->received;
        $filters = $request->filters;
        $selectAll = $request->input('select_all', false);
        $sms_slider = SMSslider::query()->first();

        $goods = goods::query()
            ->with(['User', 'flight', 'Branch'])
            ->when(!$selectAll && !in_array('on', $goodIds) && !in_array('all', $goodIds), function ($query) use ($goodIds) {
                $query->whereIn('id', $goodIds);
            })
            ->when($filters['custom_search'], function ($query, $value){
                $query->where(function($q) use ($value) {
                    $q->where("rec_name", "like", "%" . $value . "%")
                      ->orWhere("tracking_code", "like", "%" . $value . "%")
                      ->orWhere("room_number", "like", "%" . $value . "%");
                });
            })
            ->when($filters['status'], function ($query, $value){
                $query->where('flight_parcel_state', 'like', "$value");
            })
            ->when($filters['courier'], function ($query, $value){
                $query->where("uses_courier", (integer) ($value == 'yes'));
            })
            ->when($filters['has_admin_comment'], function ($query, $value){
                if ($value == 'yes')
                {
                    $query->whereNotNull('admin_comment')->where('admin_comment', '!=', '');
                }else{
                    $query->whereNull('admin_comment')->orWhere('admin_comment', '');
                }
            })
            ->when($filters['customer'], function ($query, $value){
                if ($value == "SNX") {
                    $query->where('user_room_code', "like", "" . $value . "%");
                }
                if ($value == "SNXT") {
                    $query->where('user_room_code', "like", "%" . $value . "%");
                }
                if ($value == "SNXK") {
                    $query->where('user_room_code', "like", "%" . $value . "%");
                }
                else {
                    $query->where('user_room_code', "like", "%" . "SNX" . "%");
                }
            })
            ->when($filters['declaration'], function ($query, $value){
                $query->where("is_declared", (int)($value == 'yes'));
            })
            ->when($filters['customisation'], function ($query, $value){
                $query->where("must_customize", (int)($value == 'yes'));
            })
            ->when($filters['payment'], function ($query, $value){
                $query->where("is_payed", (int)($value == 'yes'));
            })
            ->when($filters['flight_number'], function ($query, $value){
                $query->whereHas('flight', function ($query) use ($value) {
                    if (!empty($value)) {
                        $query->where('flight_number', "like", "%" . $value . "%");
                    }
                });
            })
            ->get()
        ;

        $uniqueUsers = [];
        foreach ($goods as $good)
        {
            $good->update(['customs_clearance' => (int) $good->customs_clearance != 1]);
            if($isReceived and $good->flight_parcel_state != 'RECIEVED')
            {
                $flight = $good->flight;
                $good->flight_parcel_state = 'RECIEVED';
                $good->sell_rate = $flight->usd_sell_rate ?? $good->sell_rate;
                $good->goldex_kg_price = $flight->KG_PRICE_2 ?? $good->goldex_kg_price;
                $good->price_to_pay = ($good->phisicalw * $good->goldex_kg_price) * $good->sell_rate;
                $good->is_calculated = 1;
                $good->save();

                $user = $good->user;
                if ($user->balance > 0) {
                    $good->is_payed = 1;
                    $good->save();
                }

                if ($user->user_wants_sms == 1 and !in_array($user->id, $uniqueUsers))
                {
                    $branchName = $good->branch->title_ge;
                    $endOfSmsText = '';
                    if(str_contains($branchName, 'თბილისი'))
                    {
                        $endOfSmsText = $sms_slider->delivered_to_tbilisi;
                    }
                    elseif(str_contains($branchName, 'ქუთაისი'))
                    {
                        $endOfSmsText = $sms_slider->delivered_to_kutaisi;
                    }
                    elseif(str_contains($branchName, 'ბათუმი'))
                    {
                        $endOfSmsText = $sms_slider->delivered_to_batumi;
                    }
                    elseif(str_contains($branchName, 'ზუგდიდი'))
                    {
                        $endOfSmsText = $sms_slider->delivered_to_zugdidi;
                    }
                    elseif(str_contains($branchName, 'რუსთავი'))
                    {
                        $endOfSmsText = $sms_slider->delivered_to_rustavi;
                    }
                    elseif(str_contains($branchName, 'გიორგი'))
                    {
                        $endOfSmsText = $sms_slider->delivered_to_dididighomi;
                    }

                    $smsText = $sms_slider->title_en
                        . PHP_EOL
                        . $endOfSmsText
                    ;

                    (new SMS($user->phone, $smsText))->send();
                    $uniqueUsers[] = $user->id;
                }
            }
        }

        return response()->json(['success' => true]);
    }

    public function delete2(Request $request)
    {
        //მეორე დელეიტი არის მოდალიდან წაშლის შემთხვევაში

        $id = $request->input('good_id2');
        $goods = goods::with("cashflow")->find($id);

        // ლოგირება წაშლამდე
        \App\Models\AdminActivityLog::createLog(
            'delete',
            "ამანათის წაშლა: {$goods->tracking_code}",
            'goods',
            $goods->id,
            $goods->only(['tracking_code', 'user_id', 'flight_id', 'phisicalw', 'price_to_pay']),
            null
        );

        if ($goods->cover_image != 'noimage.jpg') {
            Storage::delete('public/invoice_images/' . $goods->cover_image);
        }
        //balance დაუბრუნდეს ძველ ნიშნულს ამანათის წაშლისას


//user is balance i cvlileba
        $user = User:: where("id", $goods->user_id)->first();

        if ($goods->flight_parcel_state == 'RECIEVED' || $goods->flight_parcel_state == 'TOOK_OUT') {
            $user->balance = $user->balance + $goods->price_to_pay;
            $user->save();
        }

//        $cashflow->delete();
        //აქამდე


        $goods->delete();
        return redirect()->route("goods.index", $goods->id)->with("success", "ამანათი წარმატებით წაიშალა.");
    }

    public function delete(goods $goods)
    {
        // ლოგირება წაშლამდე
        \App\Models\AdminActivityLog::createLog(
            'delete',
            "ამანათის წაშლა: {$goods->tracking_code}",
            'goods',
            $goods->id,
            $goods->only(['tracking_code', 'user_id', 'flight_id', 'phisicalw', 'price_to_pay']),
            null
        );

        if ($goods->cover_image != 'noimage.jpg') {
            Storage::delete('public/invoice_images/' . $goods->cover_image);
        }
        $goods->delete();
        return redirect()->back()->with("success", "ამანათი წარმატებით წაიშალა.");
    }


    //admin declaration goods index.

    public function declaration()
    {
        $itemcategory = ItemCategory::all();
        $flights = Flight::all();
        return view('admin.goods.index', compact('flights', 'itemcategory'));
    }

//    admin courier goods index
    public function courier()
    {
        $itemcategory = ItemCategory::all();
        $flights = Flight::all();
        return view('admin.courier.index', compact('flights', 'itemcategory'));
    }

    public function courier2()
    {
        $itemcategory = ItemCategory::all();
        $flights = Flight::all();
        $city = city::all();
//        $currier_number = Goods::query()
//            ->where('uses_courier', '=', 1)
//            ->where('goods.flight_parcel_state', '!=', 'TOOK_OUT');
//        $currier_count = $currier_number->count();

        return view('admin.courier.index2', compact('flights', 'itemcategory', 'city'));
    }

    // find goods by id admin
    public function findGoodById(Request $request)
    {
        $request->validate([
            "id" => "required|integer"
        ]);
        $goods = goods::query()->find($request->input("id"));
        return response($goods, "200");
    }
//    public function mass_update(Request $request)
//    {
//        $request->validate([
//            "id" => "required|integer"
//        ]);
//        $captured_id = $request->input("id");
//        $goods = Goods::where("id", $captured_id);
//        foreach ($goods as $good){
//            $goods->
//        }
//        return response($goods, "200");
//    }

    //admin declaration update
    public function declaration_update(Declaration $request)
    {
//        dd($request->all());
        $request->validate([
            'tracking_code' => 'required',
            'client_buy_amount' => 'required',
            'category_id' => 'required',
            'cover_image' => 'image|nullable|max:1999',
            'customize' => 'nullable'
        ]);
        if ($request->hasFile('cover_image')) {

//            get filename
            $filenameWithExt = $request->file('cover_image')->getClientOriginalName();
            //get just filename
            $filename = pathinfo($filenameWithExt, PATHINFO_FILENAME);
            //get just extensions
            $extension = $request->file('cover_image')->getClientOriginalExtension();
            // Filename to store
            $fileNameToStore = $filename . '_' . time() . '.' . $extension;
            // Upload Image
            $path = $request->file('cover_image')->storeAs('public/invoice_images', $fileNameToStore);

        } else {
            $fileNameToStore = 'noimage.jpg';
        }
//        dzveli
        $goods = goods::find($request->input('good_id'));
        $goods->tracking_code = $request->input('tracking_code');
        $goods->sender_company = $request->input('sender_company');
        $goods->client_buy_amount = $request->input('client_buy_amount');
        $goods->category_id = $request->input('category_id');
        $goods->is_declared = true;

        if ($request->has('customize') || $goods->client_buy_amount >= 300) {
            $goods->must_customize = 1;
        } else {
            $goods->must_customize = 0;
        }

        if ($request->hasFile('cover_image')) {
            $goods->cover_image = $fileNameToStore;
        }
        $goods->save();

        return redirect()->route("declaration.index")->with("success", "ამანათი წარმატებით დარედაქტირდა.");
    }

    public function declaration_update3(Declaration $request)
    {
//        dd($request->all());
        $request->validate([
            'tracking_code' => 'required',
            'client_buy_amount' => 'required',
            'category_id' => 'required',
            'cover_image' => 'image|nullable|max:1999',
            'customize' => 'nullable'
        ]);
        if ($request->hasFile('cover_image')) {

//            get filename
            $filenameWithExt = $request->file('cover_image')->getClientOriginalName();
            //get just filename
            $filename = pathinfo($filenameWithExt, PATHINFO_FILENAME);
            //get just extensions
            $extension = $request->file('cover_image')->getClientOriginalExtension();
            // Filename to store
            $fileNameToStore = $filename . '_' . time() . '.' . $extension;
            // Upload Image
            $path = $request->file('cover_image')->storeAs('public/invoice_images', $fileNameToStore);

        } else {
            $fileNameToStore = 'noimage.jpg';
        }
//        dzveli
        $goods = goods::find($request->input('good_id'));
        $goods->tracking_code = $request->input('tracking_code');
        $goods->sender_company = $request->input('sender_company');
        $goods->client_buy_amount = $request->input('client_buy_amount');
        $goods->category_id = $request->input('category_id');
        $goods->is_declared = true;

        if ($request->has('customize') || $goods->client_buy_amount >= 300) {
            $goods->must_customize = 1;
        } else {
            $goods->must_customize = 0;
        }

        if ($request->hasFile('cover_image')) {
            $goods->cover_image = $fileNameToStore;
        }
        $goods->save();

        return redirect()->route("goods.index3")->with("success", "ამანათი წარმატებით დარედაქტირდა.");
    }

    //user declaration update
    public function declaration_update2(Declaration $request)
    {

//        $goods = Goods::find($goods->id);

        $request->validate([
            'tracking_code' => 'required',
            'client_buy_amount' => 'required',
            'category_id' => 'required',
            'cover_image' => 'image|nullable|max:1999',
        ]);
//        handle file upload
        if ($request->hasFile('cover_image')) {

//            get filename
            $filenameWithExt = $request->file('cover_image')->getClientOriginalName();
            //get just filename
            $filename = pathinfo($filenameWithExt, PATHINFO_FILENAME);
            //get just extensions
            $extension = $request->file('cover_image')->getClientOriginalExtension();
            // Filename to store
            $fileNameToStore = $filename . '_' . time() . '.' . $extension;
            // Upload Image
            $path = $request->file('cover_image')->storeAs('public/invoice_images', $fileNameToStore);

        } else {
            $fileNameToStore = 'noimage.jpg';
        }
//        nikas kodi
//        $folder = 'user/'.$id.'/';

        $goods = goods::find($request->input('good_id'));

        if (Auth::user()->id == $goods->user_id) {
            $goods->tracking_code = $request->input('tracking_code');
            $goods->sender_company = $request->input('sender_company');
            $goods->client_buy_amount = $request->input('client_buy_amount');
            $goods->category_id = $request->input('category_id');
            $goods->is_declared = true;
            if ($request->hasFile('cover_image')) {
                $goods->cover_image = $fileNameToStore;
            }

            if ($goods->client_buy_amount >= 300) {
                $goods->must_customize = true;
            } else {
                $goods->must_customize = 0;
            }

            $goods->save();
        }


        return redirect()->back()->with("success", "ამანათი წარმატებით დეკლარირდა.");
    }

    public function courier_user(goods $goods, Request $request)
    {
//        $goods = Goods::find($goods->id);
        $validated = $request->validate([
            'address_1' => 'required',
            'address_2' => 'required',
        ]);
        $goods = goods::find($request->input('good_id2'));

        if ($request->action_type == 'cancelled')
        {
            $goods->uses_courier = false;
            $goods->address_1 = null;
            $goods->delivery_comment = null;
            $goods->address_2 = null;
            $goods->took_out_date = null;
            $goods->file_path = null;
            $goods->save();
        }else{
            $goods->uses_courier = true;
            $goods->address_1 = $request->input('address_1');
            $goods->delivery_comment = $request->input('delivery_comment');
            $goods->address_2 = $request->input('address_2');
            $goods->took_out_date = now();
            $goods->file_path = 'გასაკეთებელი';
            $goods->save();
        }


        return redirect()->back()->with("success", "საკურიერო მომსახურება წარმატებით დაემატა.");
    }

    public function dashboard(Request $request)
    {
        $users_count = User::all()->count();
        $flights_count = Flight::all()->count();
        $statistics = new StatisticService;
        $flights = Flight::with('parcelCoordinates')
            ->limit(100)
            ->orderByDesc('id')
            ->get();
        $countries = ParcelCoordinates::all();
        $statisticTable = \App\Models\Branch::all();
        $totalDebt = $statistics->getTotalDebtAmount();

        return view('admin.dashboard.index', compact('request', 'users_count', 'flights_count', 'statistics', 'flights', 'countries', 'statisticTable', 'totalDebt'));
    }

    public function scanParcel()
    {
        // Retrieve the parcel from the database
//        $scanned_good = Goods::find($id);
//
//        if (!$scanned_good ) {
//            return response()->json(['message' => 'Parcel not found'], 404);
//        }
//        dd($scanned_good);

        return view('admin.goods.scan');
//        return redirect()->route("goods.index", $goods->id)->with("success", "ამანათი წარმატებით დარექაქტირდა.");

    }

    public function deletedGoods(Request $request)
    {
//        $perPage = $request->input('per_page', 10); // Default to 10 items per page if not specified
//        $data = goods::query()
//            ->withoutGlobalScopes()
//            ->whereNotNull('deleted_at')
//            ->paginate($perPage);
//
        return view('admin.goods.deleted-index'
//        ,compact('data', 'perPage')
        );
    }

    public function deletedGoodsData(Request $request)
    {
        $perPage = $request->input('pagination.perpage', 10);
        $keyword = $request->input('query.keyword', '');
        $page = $request->input('pagination.page', 1);

        $query = goods::query()
            ->withoutGlobalScopes()
            ->whereNotNull('deleted_at')
            ->orderByDesc('id')
            ->with(['user', 'flight'])
            ->when($keyword, function ($query, $value) {
                $query->where(function ($query) use ($value){
                    $query->whereHas('user', function ($query) use ($value) {
                        $query
                            ->where(DB::raw("CONCAT(first_name_ge, ' ', last_name_ge)"), 'like', "%$value%")
                            ->orWhere(DB::raw("CONCAT(last_name_ge, ' ', first_name_ge)"), 'like', "%$value%")
                            ->orWhere('user_room_code', 'like', "%$value%");
                    })
                        ->orWhereHas('flight', function ($query) use ($value){
                            $query->where('flight_number', 'like', "%$value%");
                        })
                        ->orWhere('tracking_code', 'like', "%$value%")
                        ->orWhere('deleted_at', 'like', "%$value%")
                    ;
                });
            })
        ;

        $totalRecords = $query->count();

        $data = $query->paginate($perPage, ['*'], 'page', $page);

        $response = [
            'meta' => [
                'page' => $data->currentPage(),
                'pages' => $data->lastPage(),
                'perpage' => $data->perPage(),
                'total' => $totalRecords
            ],
            'data' => $data->items()
        ];

        return response()->json($response);
    }

    public function recoverDeletedGood($id): RedirectResponse
    {
        $parcel = goods::query()
            ->withoutGlobalScopes()
            ->find($id);
        $parcel->deleted_at = null;
        $parcel->save();

        return redirect()->route('deleted.goods')->with(['success' => 'წარმატებით აღდგა']);
    }

    public function branchLog(Request $request)
    {
        return view('admin.branch.log');
    }

    public function getBranchLogData(Request $request)
    {
        $perPage = $request->input('pagination.perpage', 10);
        $keyword = $request->input('query.keyword', '');
        $page = $request->input('pagination.page', 1);
        $flightId = $request->input('flight_id');

        $query = UserBranchChangeLog::query()
            ->with(['user', 'branchFrom', 'branchTo', 'good.flight'])
            ->orderByDesc('id')
            ->when($flightId, function ($query, $value) {
                $query->whereHas('good.flight', function ($query) use ($value) {
                    $query->where('id', $value);
                });
            })
            ->when($keyword, function ($query, $value) {
                $query
                    ->whereHas('user', function ($query) use ($value) {
                        $query
                            ->where(DB::raw("CONCAT(first_name_ge, ' ', last_name_ge)"), 'like', "%$value%")
                            ->orWhere(DB::raw("CONCAT(last_name_ge, ' ', first_name_ge)"), 'like', "%$value%")
                            ->orWhere('user_room_code', 'like', "%$value%");
                    })
                    ->orWhereHas('branchFrom', function ($query) use ($value){
                        $query->where('title_ge', 'like', "%$value%");
                    })->orWhereHas('branchTo', function ($query) use ($value){
                        $query->where('title_ge', 'like', "%$value%");
                    })
                    ->orWhere('tracking_code', 'like', "%$value%")
                    ->orWhere('created_at', 'like', "%$value%")
                ;
            })
        ;


        $totalRecords = $query->count();

        $data = $query->paginate($perPage, ['*'], 'page', $page);

        $response = [
            'meta' => [
                'page' => $data->currentPage(),
                'pages' => $data->lastPage(),
                'perpage' => $data->perPage(),
                'total' => $totalRecords
            ],
            'data' => $data->items()
        ];

        return response()->json($response);
    }

    public function tookOutGoods(Request $request)
    {
        $branches = Branch::all();
        return view('admin.goods.took-out', compact('branches'));
    }

    public function tookOutGoodsData(Request $request)
    {
        $perPage = $request->input('pagination.per_page', 10);
        $keyword = $request->input('query.keyword', '');
        $branchId = $request->input('branch_id');
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $type = $request->input('type');

        $page = $request->input('pagination.page', 1);
        $sortField = $request->input('sort.field', 'id'); // Default sorting field
        $sortOrder = $request->input('sort.sort', 'asc'); // Default sorting order

        $query = TookOutScanHistory::query()
            ->with(['user', 'good.flight', 'good.user', 'good.branch'])
            ->orderByDesc('id')
            ->when($keyword, function ($query, $value) {
                $query
                    ->whereHas('user', function ($query) use ($value) {
                        $query
                            ->where(DB::raw("CONCAT(first_name_ge, ' ', last_name_ge)"), 'like', "%$value%")
                            ->orWhere(DB::raw("CONCAT(last_name_ge, ' ', first_name_ge)"), 'like', "%$value%")
                            ->orWhere('phone', 'like', "%$value%");
                    })
                    ->orWhere('tracking_code', 'like', "%$value%")
                    ->orWhereHas('good.flight', function ($query) use ($value){
                        $query->where('flight_number', 'like', "%$value%");
                    })
                    ->orWhereHas('good.user', function ($query) use ($value) {
                        $query
                            ->where(DB::raw("CONCAT(first_name_ge, ' ', last_name_ge)"), 'like', "%$value%")
                            ->orWhere(DB::raw("CONCAT(last_name_ge, ' ', first_name_ge)"), 'like', "%$value%")
                            ->orWhere('phone', 'like', "%$value%");
                    })
                    ->orWhereHas('good.user', function ($query) use ($value) {
                        $query->where('user_room_code', 'like', "%$value%");
                    })
                    ->orWhereHas('good.branch', function ($query) use ($value) {
                        $query->where('title_ge', 'like', "%$value%");
                    })
                ;
            })
            ->when($type == 2 and $type != null, function ($query){
                $query->where('type', 1);
            })
            ->when($type == 3, function ($query){
                $query->where('type', 0);
            })
            ->when($branchId, function ($query, $value){
                $query->whereHas('good', function ($query) use ($value){
                    $query->where('branch_id', $value);
                });
            })
            ->when($startDate && $endDate, function ($query) use ($startDate, $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            })
            ->when($startDate && !$endDate, function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate);
            })
            ->when(!$startDate && $endDate, function ($query) use ($endDate) {
                $query->where('created_at', '<', $endDate);
            })
        ;


        $totalRecords = $query->count();

        $data = $query->paginate($perPage, ['*'], 'page', $page);

        $response = [
            'meta' => [
                'page' => $data->currentPage(),
                'pages' => $data->lastPage(),
                'perpage' => $data->perPage(),
                'total' => $totalRecords
            ],
            'data' => $data->items()
        ];

        return response()->json($response);
    }


}
